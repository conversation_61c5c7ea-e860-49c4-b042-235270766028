import { CampaignConfig } from './ScoutCampaign';

/**
 * Pre-configured campaign templates for common use cases
 */

/**
 * Japanese Travel KOLs Campaign Template
 */
export const japaneseTravelKOLsTemplate: Partial<CampaignConfig> = {
  campaignName: 'Japanese Travel KOLs Campaign',
  description:
    'Find Japanese travel-focused KOLs with 2k-20k followers for brand collaboration',
  targetKOLCount: 500,
  kolPerTask: 100,
  maxWorkflowRuns: 8,
  sharedConfig: {
    targetCreatorDescription: `Find me Japanese KOLs:
    - Ethnicity: Pure Japanese creators only (from their text/title/desc language and their appearance, not from metadata of region or language field)
    - Follower Count: Between 2,000 to 20,000 followers
    - Content Type:
      - Lifestyle
      - Everyday content
      - Vlog
      - Japan travel
    - Visual Constraints (thumbnail filtering only):
      - Must contains outdoor scenes on any thumbnail
      - Must contains face on any thumbnail`,
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 2000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Gaming Creators Campaign Template
 */
export const gamingCreatorsTemplate: Partial<CampaignConfig> = {
  campaignName: 'Gaming Creators Campaign',
  description: 'Find gaming content creators with diverse game coverage',
  targetKOLCount: 300,
  kolPerTask: 75,
  maxWorkflowRuns: 6,
  sharedConfig: {
    targetCreatorDescription: `Find me gaming creators with following rules:
    1. They post videos about different games, not just one specific game. List out their recently posted games.
    2. The median view count of their recent videos must greater than 50k.
    3. They speak English.
    4. They show their faces and have voiceovers in their videos.
    5. All posts are original content.`,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    minViews: 10000,
    minLikes: 500,
    minComments: 50,
    minFollowers: 5000,
    minRecentMedianViews: 50000,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 3,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Fashion & Lifestyle Campaign Template
 */
export const fashionLifestyleTemplate: Partial<CampaignConfig> = {
  campaignName: 'Fashion & Lifestyle Campaign',
  description: 'Find fashion and lifestyle influencers for brand partnerships',
  targetKOLCount: 400,
  kolPerTask: 80,
  maxWorkflowRuns: 7,
  sharedConfig: {
    targetCreatorDescription: `Find me fashion and lifestyle creators:
    - Content Type: Fashion, beauty, lifestyle, daily outfits
    - Follower Count: Between 10,000 to 100,000 followers
    - Engagement: High engagement rate with authentic audience
    - Content Quality: High-quality photos/videos, consistent posting
    - Demographics: Primarily female audience aged 18-35
    - Language: English speaking
    - Style: Trendy, relatable, authentic content`,
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE' as const,
    minViews: 5000,
    minLikes: 200,
    minComments: 20,
    minFollowers: 10000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Tech & Innovation Campaign Template
 */
export const techInnovationTemplate: Partial<CampaignConfig> = {
  campaignName: 'Tech & Innovation Campaign',
  description: 'Find tech reviewers and innovation content creators',
  targetKOLCount: 200,
  kolPerTask: 50,
  maxWorkflowRuns: 6,
  sharedConfig: {
    targetCreatorDescription: `Find me tech and innovation creators:
    - Content Type: Tech reviews, gadget unboxing, innovation content
    - Follower Count: Between 20,000 to 200,000 followers
    - Expertise: Knowledgeable about technology trends
    - Content Quality: Detailed reviews, honest opinions
    - Demographics: Tech-savvy audience
    - Language: English speaking
    - Style: Educational, informative, trustworthy`,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    minViews: 15000,
    minLikes: 800,
    minComments: 100,
    minFollowers: 20000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 3,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Food & Cooking Campaign Template
 */
export const foodCookingTemplate: Partial<CampaignConfig> = {
  campaignName: 'Food & Cooking Campaign',
  description:
    'Find food and cooking content creators for culinary brand partnerships',
  targetKOLCount: 350,
  kolPerTask: 70,
  maxWorkflowRuns: 7,
  sharedConfig: {
    targetCreatorDescription: `Find me food and cooking creators:
    - Content Type: Cooking tutorials, recipe videos, food reviews
    - Follower Count: Between 5,000 to 50,000 followers
    - Content Quality: Clear cooking instructions, appetizing visuals
    - Engagement: Active community, recipe sharing
    - Demographics: Food enthusiasts, home cooks
    - Language: English speaking
    - Style: Approachable, easy-to-follow recipes`,
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE' as const,
    minViews: 3000,
    minLikes: 150,
    minComments: 25,
    minFollowers: 5000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Fitness & Wellness Campaign Template
 */
export const fitnessWellnessTemplate: Partial<CampaignConfig> = {
  campaignName: 'Fitness & Wellness Campaign',
  description:
    'Find fitness and wellness influencers for health brand partnerships',
  targetKOLCount: 300,
  kolPerTask: 60,
  maxWorkflowRuns: 7,
  sharedConfig: {
    targetCreatorDescription: `Find me fitness and wellness creators:
    - Content Type: Workout routines, wellness tips, healthy lifestyle
    - Follower Count: Between 15,000 to 100,000 followers
    - Content Quality: Professional workout videos, health advice
    - Engagement: Motivational community, fitness challenges
    - Demographics: Health-conscious audience aged 20-45
    - Language: English speaking
    - Style: Motivational, educational, authentic`,
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE' as const,
    minViews: 8000,
    minLikes: 400,
    minComments: 50,
    minFollowers: 15000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 4,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Test Campaign Template (for development and testing)
 */
export const testCampaignTemplate: Partial<CampaignConfig> = {
  campaignName: 'Test Campaign',
  description: 'Small test campaign for development and testing purposes',
  targetKOLCount: 50,
  kolPerTask: 25,
  maxWorkflowRuns: 3,
  sharedConfig: {
    targetCreatorDescription: `Find me any creators for testing:
    - Content Type: Any type of content
    - Follower Count: Between 1,000 to 10,000 followers
    - Language: Any language
    - Content: Any original content`,
    useIntelligentChallengeSelection: true,
    filterMode: 'LOOSE' as const,
    minViews: 100,
    minLikes: 10,
    minComments: 1,
    minFollowers: 1000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
  },
  concurrentTasksLimit: 2,
  persistenceType: 'json',
  enableProgressiveReporting: true,
  reportingInterval: 1,
};

/**
 * Helper function to create a campaign config from a template
 */
export function createCampaignFromTemplate(
  template: Partial<CampaignConfig>,
  overrides: Partial<CampaignConfig> = {},
): CampaignConfig {
  const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
  const campaignId =
    overrides.campaignId ||
    `${template.campaignName?.toLowerCase().replace(/\s+/g, '-')}-${timestamp}`;

  const outputDirectory =
    overrides.outputDirectory || `./campaign-results/${campaignId}`;

  return {
    campaignId,
    outputDirectory,
    ...template,
    ...overrides,
    sharedConfig: {
      ...template.sharedConfig,
      ...overrides.sharedConfig,
    },
  } as CampaignConfig;
}

/**
 * Available campaign templates
 */
export const campaignTemplates = {
  japaneseTravel: japaneseTravelKOLsTemplate,
  gaming: gamingCreatorsTemplate,
  fashion: fashionLifestyleTemplate,
  tech: techInnovationTemplate,
  food: foodCookingTemplate,
  fitness: fitnessWellnessTemplate,
  test: testCampaignTemplate,
};

/**
 * Get template by name
 */
export function getTemplate(
  name: keyof typeof campaignTemplates,
): Partial<CampaignConfig> {
  return campaignTemplates[name];
}

/**
 * List all available templates
 */
export function listTemplates(): Array<{ name: string; description: string }> {
  return [
    {
      name: 'japaneseTravel',
      description: 'Japanese travel-focused KOLs (2k-20k followers)',
    },
    {
      name: 'gaming',
      description: 'Gaming creators with diverse game coverage (5k+ followers)',
    },
    {
      name: 'fashion',
      description: 'Fashion & lifestyle influencers (10k-100k followers)',
    },
    {
      name: 'tech',
      description:
        'Tech reviewers and innovation creators (20k-200k followers)',
    },
    {
      name: 'food',
      description: 'Food & cooking content creators (5k-50k followers)',
    },
    {
      name: 'fitness',
      description: 'Fitness & wellness influencers (15k-100k followers)',
    },
    {
      name: 'test',
      description: 'Small test campaign for development (1k-10k followers)',
    },
  ];
}
