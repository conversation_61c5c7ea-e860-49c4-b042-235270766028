import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import {
  createCampaignFromTemplate,
  getTemplate,
  listTemplates,
} from '@/campaigns/templates';
import { Mastra } from '@mastra/core';
import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';

// Register the workflow and agents
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { creatorScoutWorkflow },
});

/**
 * Demo function for Scout Campaign feature
 */
async function runScoutCampaignDemo() {
  console.log('🚀 Starting Scout Campaign Demo...\n');

  // Define campaign configuration
  const campaignConfig: CampaignConfig = {
    // Campaign identification
    campaignId: 'japanese-travel-kols-ctrip',
    campaignName: 'Japanese Travel KOLs Campaign (Ctrip)',
    description:
      'Find Japanese travel-focused KOLs with 2k-20k followers for brand collaboration',

    // Campaign-level targets
    targetKOLCount: 500, // Total KOLs needed across all workflows
    kolPerTask: 100, // KOLs per individual workflow run
    maxWorkflowRuns: 10, // Maximum workflow runs to prevent infinite loops

    // Shared workflow configuration
    sharedConfig: {
      targetCreatorDescription: `Find me some Japanese KOLs:
      - Ethnicity: Pure Japanese creators only (from their text/title/desc language and their appearance, not from metadata of region or language field)
      - Follower Count: Between 2,000 to 20,000 followers
      - Content Type:
        - Lifestyle
        - Everyday content
        - Vlog
        - Japan travel
      - Visual Constraints (thumbnail filtering only):
        - Must contains outdoor scenes on any thumbnail
        - Must contains face on any thumbnail`,
      useIntelligentChallengeSelection: true,
      filterMode: 'LOOSE' as const,
      minViews: 0,
      minLikes: 0,
      minComments: 0,
      minFollowers: 2000, // Minimum 2k followers as specified
      minRecentMedianViews: 0,
      minRecentMedianComments: 0,
      minRecentMedianLikes: 0,
    },

    // Campaign execution settings
    concurrentTasksLimit: 4, // Respect rate limits
    persistenceType: 'json',
    outputDirectory: './campaign-results/japanese-travel-kols-ctrip',

    // Progressive reporting settings
    enableProgressiveReporting: true,
    reportingInterval: 1, // Report after every workflow run
  };

  try {
    // Create and run the campaign
    const campaign = new ScoutCampaign(campaignConfig, mastra);

    console.log('📋 Campaign Configuration:');
    console.log(`   🎯 Target KOLs: ${campaignConfig.targetKOLCount}`);
    console.log(`   📊 KOLs per task: ${campaignConfig.kolPerTask}`);
    console.log(`   🔄 Max workflow runs: ${campaignConfig.maxWorkflowRuns}`);
    console.log(`   💾 Output directory: ${campaignConfig.outputDirectory}`);
    console.log(`   🔧 Filter mode: ${campaignConfig.sharedConfig.filterMode}`);
    console.log(
      `   👥 Min followers: ${campaignConfig.sharedConfig.minFollowers}`,
    );
    console.log('');

    // Run the campaign
    const campaignResults = await campaign.runCampaign();

    console.log('\n🎉 Campaign completed successfully!');
    console.log(`📊 Total batches processed: ${campaignResults.length}`);

    // Display summary of each batch
    campaignResults.forEach((batch) => {
      console.log(`\n📋 Batch ${batch.batchNumber} Summary:`);
      console.log(`   🆕 New unique KOLs: ${batch.newUniqueKOLs}`);
      console.log(`   📊 Total unique KOLs: ${batch.totalUniqueKOLs}`);
      console.log(
        `   ⏱️ Execution time: ${formatDuration(batch.executionTime)}`,
      );
      console.log(`   📅 Timestamp: ${batch.timestamp}`);
    });

    // Display final campaign status
    const finalStatus = campaign.getCampaignStatus();
    console.log('\n📈 Final Campaign Statistics:');
    console.log(
      `   🎯 Total unique KOLs found: ${finalStatus.totalUniqueKOLs}`,
    );
    console.log(
      `   📊 Total scouted results: ${finalStatus.totalScoutedResults}`,
    );
    console.log(
      `   🔄 Workflow runs completed: ${finalStatus.workflowRunsCompleted}`,
    );
    console.log(
      `   ✅ Successful runs: ${finalStatus.statistics.successfulRuns}`,
    );
    console.log(`   ❌ Failed runs: ${finalStatus.statistics.failedRuns}`);
    console.log(
      `   📈 Average KOLs per run: ${finalStatus.statistics.averageKOLsPerRun.toFixed(1)}`,
    );
    console.log(
      `   ⏱️ Total execution time: ${formatDuration(finalStatus.statistics.totalExecutionTime)}`,
    );
  } catch (error) {
    console.error('❌ Campaign failed:', error);
    process.exit(1);
  }
}

/**
 * Demo function for a smaller test campaign using templates
 */
async function runSmallTestCampaign() {
  console.log('🧪 Starting Small Test Campaign...\n');

  // Use the test template
  const testCampaignConfig = createCampaignFromTemplate(getTemplate('test'), {
    campaignId: 'test-campaign-small',
    targetKOLCount: 50,
    kolPerTask: 25,
    maxWorkflowRuns: 3,
  });

  try {
    const campaign = new ScoutCampaign(testCampaignConfig, mastra);
    const results = await campaign.runCampaign();

    console.log('\n✅ Test campaign completed!');
    console.log(`📊 Batches processed: ${results.length}`);
    console.log(
      `🎯 Total KOLs found: ${campaign.getCampaignStatus().totalUniqueKOLs}`,
    );
  } catch (error) {
    console.error('❌ Test campaign failed:', error);
  }
}

/**
 * Demo function showing available templates
 */
async function runTemplateDemo() {
  console.log('📋 Available Campaign Templates:\n');

  const templates = listTemplates();
  templates.forEach((template, index) => {
    console.log(`${index + 1}. ${template.name}: ${template.description}`);
  });

  console.log('\n🎯 Creating campaigns from templates...\n');

  // Demo creating campaigns from different templates
  const gamingCampaign = createCampaignFromTemplate(getTemplate('gaming'), {
    campaignId: 'gaming-demo-2024',
    targetKOLCount: 100,
  });

  const fashionCampaign = createCampaignFromTemplate(getTemplate('fashion'), {
    campaignId: 'fashion-demo-2024',
    targetKOLCount: 150,
  });

  console.log('🎮 Gaming Campaign Config:');
  console.log(`   Name: ${gamingCampaign.campaignName}`);
  console.log(`   Target: ${gamingCampaign.targetKOLCount} KOLs`);
  console.log(`   Min Followers: ${gamingCampaign.sharedConfig.minFollowers}`);

  console.log('\n👗 Fashion Campaign Config:');
  console.log(`   Name: ${fashionCampaign.campaignName}`);
  console.log(`   Target: ${fashionCampaign.targetKOLCount} KOLs`);
  console.log(`   Min Followers: ${fashionCampaign.sharedConfig.minFollowers}`);

  console.log('\n✅ Template demo completed!');
}

/**
 * Demo function showing campaign pause/resume functionality
 */
async function runPauseResumeCampaignDemo() {
  console.log('⏸️ Starting Pause/Resume Campaign Demo...\n');

  const pauseResumeCampaignConfig: CampaignConfig = {
    campaignId: 'pause-resume-demo',
    campaignName: 'Pause Resume Demo Campaign',
    description: 'Demo campaign to show pause/resume functionality',

    targetKOLCount: 100,
    kolPerTask: 30,
    maxWorkflowRuns: 5,

    sharedConfig: {
      targetCreatorDescription:
        'Find lifestyle and fashion creators with 5k-50k followers',
      useIntelligentChallengeSelection: true,
      filterMode: 'STRICT' as const,
      minViews: 0,
      minLikes: 0,
      minComments: 0,
      minFollowers: 5000,
      minRecentMedianViews: 0,
      minRecentMedianComments: 0,
      minRecentMedianLikes: 0,
    },

    concurrentTasksLimit: 3,
    persistenceType: 'json',
    outputDirectory: './campaign-results/pause-resume-demo',
    enableProgressiveReporting: true,
    reportingInterval: 1,
  };

  try {
    const campaign = new ScoutCampaign(pauseResumeCampaignConfig, mastra);

    console.log('📋 Campaign created, checking status...');
    console.log('Status:', campaign.getCampaignStatus().status);

    // Simulate pausing the campaign
    console.log('\n⏸️ Pausing campaign...');
    campaign.pauseCampaign();
    console.log('Status after pause:', campaign.getCampaignStatus().status);

    // Simulate resuming the campaign
    console.log('\n▶️ Resuming campaign...');
    campaign.resumeCampaign();
    console.log('Status after resume:', campaign.getCampaignStatus().status);

    // Note: In a real scenario, you would run the campaign here
    // const results = await campaign.runCampaign();

    console.log('\n✅ Pause/Resume demo completed!');
  } catch (error) {
    console.error('❌ Pause/Resume demo failed:', error);
  }
}

/**
 * Utility function to format duration
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Main function to run different demo scenarios
 */
async function main() {
  const args = process.argv.slice(2);
  const demoType = args[0] || 'full';

  console.log('🎯 Scout Campaign Demo System');
  console.log('============================\n');

  switch (demoType) {
    case 'full':
      console.log('Running full campaign demo...');
      await runScoutCampaignDemo();
      break;

    case 'test':
      console.log('Running small test campaign...');
      await runSmallTestCampaign();
      break;

    case 'pause-resume':
      console.log('Running pause/resume demo...');
      await runPauseResumeCampaignDemo();
      break;

    case 'templates':
      console.log('Running template demo...');
      await runTemplateDemo();
      break;

    default:
      console.log('Available demo types:');
      console.log('  full         - Run full campaign demo');
      console.log('  test         - Run small test campaign');
      console.log('  pause-resume - Demo pause/resume functionality');
      console.log('  templates    - Show available campaign templates');
      console.log('\nUsage: npm run demo:campaign [demo-type]');
      break;
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export {
  runScoutCampaignDemo,
  runSmallTestCampaign,
  runPauseResumeCampaignDemo,
  formatDuration,
};
